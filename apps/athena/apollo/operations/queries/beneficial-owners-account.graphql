query BeneficialOwnersAccount($id: ID!, $withLatestHolding: Boolean = false) {
  beneficialOwnerAccount(id: $id) {
    id

    accountName
    addressCity
    addressCountry
    addressLineOne
    addressLineTwo
    addressPostcode
    addressState

    latestHolding @include(if: $withLatestHolding) {
      id
      shares
      parent {
        beneficialOwnerAccount {
          accountName
        }
      }
      shareholding {
        accountName
      }
    }
  }
}
