import { FC, useState, useMemo, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, Typo<PERSON>, Toolt<PERSON> } from '@ds';
import { ChevronUpIcon } from '@heroicons/react-v2/24/outline';
import dayjs from 'dayjs';
import { useRouter } from 'next/router';
import numeral from 'numeral';
import LinkContactModal from '@/components/contacts/profile/modals/link-contact';
import { useBoFullReportTableContext } from '@/components/investors/beneficial-owners/reports/full-report/context';
import FullReportRow from '@/components/investors/beneficial-owners/reports/full-report/row';
import { useBoReportTableContext } from '@/components/investors/beneficial-owners/reports/report-context';
import { ParticipantWithRank } from '@/components/investors/beneficial-owners/reports/report-context';
import TableEmpty from '@/components/utils/tables/empty';
import TableHeader, { Header } from '@/components/utils/tables/table-header';
import { useCurrentCompanyProfileUser } from '@/contexts/current-company-profile-user-context';

const FullReportTable: FC = () => {
  const {
    error,
    filters,
    loading,
    participantsFiltered,
    previousReportDate,
    search,
    setFilters,
    setSearch,
  } = useBoReportTableContext();
  const {
    postFilteringStats: {
      totalBeneficialOwners,
      totalChange,
      totalLayer1Participants,
      totalPortion,
      totalShares,
    },
  } = useBoFullReportTableContext();

  const { isUK, translate } = useCurrentCompanyProfileUser();

  // read the url params
  const router = useRouter();
  useEffect(() => {
    if (router.query.search && !loading) {
      setSearch(router.query.search as string);
    }
  }, [router.query.search, setSearch, loading]);

  const [linkContactModalData, setLinkContactModalData] = useState<{
    contact?: { id?: string | null } | null;
    shareholdingOrAccount: {
      id: string;
      type: 'Registry' | 'Beneficial owner' | 'Intermediary';
    } | null;
  } | null>(null);

  const [showBackToTop, setShowBackToTop] = useState(false);

  const openLinkContactModal = (data: {
    contact?: { id?: string | null } | null;
    shareholdingOrAccount: {
      id: string;
      type: 'Registry' | 'Beneficial owner' | 'Intermediary';
    } | null;
  }) => setLinkContactModalData(data);

  const closeLinkContactModal = () => setLinkContactModalData(null);

  useEffect(() => {
    const handleScroll = () => {
      setShowBackToTop(window.scrollY > 500);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const scrollToTop = () => {
    window.scrollTo({
      behavior: 'smooth',
      top: 0,
    });
  };

  const changedSinceTitle = previousReportDate
    ? `Change since ${dayjs(previousReportDate).format('DD/MM/YYYY')}`
    : 'Change since last report';

  const columns: Header[] = [
    {
      className: 'sm:w-[64px] min-w-[64px] whitespace-nowrap lg:pl-3',
      label: 'Rank',
    },
    {
      className: 'min-w-[380px] whitespace-nowrap',
      label: translate('beneficialOwners.accountTableHeader'),
    },
    ...(isUK
      ? [
          {
            className: 'sm:w-[100px]',
            label: 'Depot / Designation',
            node: () => (
              <Tooltip content="Depot / Designation">
                <Typography
                  className="text-base text-gray-500"
                  variant="text-button-sm"
                >
                  Depot / Designation
                </Typography>
              </Tooltip>
            ),
          },
        ]
      : []),
    {
      className: 'sm:w-[115px] sm:min-w-[115px]',
      label: 'Status',
    },
    {
      className: 'sm:w-[150px] min-w-[110px] whitespace-nowrap text-right',
      label: 'Shares (#)',
      value: 'shares',
    },
    {
      className: 'sm:min-w-[100px] text-right',
      label: '% of total holdings',
    },
    {
      className: 'sm:min-w-[130px] text-right',
      label: changedSinceTitle,
    },
    {
      className: 'sm:w-[100px] sm:min-w-[100px]',
      label: 'Type',
    },
    {
      className: 'sm:w-[100px] sm:min-w-[100px]',
      label: 'Location',
    },
    {
      className: 'lg:p-3 lg:pl-3 sm:min-w-[240px] sm:w-[240px]',
      label: 'Linked contact',
    },
  ];
  const sortableColumns = [
    'Shares (#)',
    '% of total holdings',
    changedSinceTitle,
  ];
  const [sortedColumn, setSortedColumn] = useState({
    column: 'Shares (#)',
    desc: true,
  });

  const participantsSorted = useMemo(() => {
    const recursiveSort = (
      participants: ParticipantWithRank[]
    ): ParticipantWithRank[] => {
      const sorted = [...participants];
      const { column, desc } = sortedColumn;

      if (column === 'Shares (#)') {
        sorted.sort(({ shares: a }, { shares: b }) => (desc ? b - a : a - b));
      }
      if (column === '% of total holdings') {
        sorted.sort(({ portionOfTotal: a }, { portionOfTotal: b }) =>
          desc ? (b || 0) - (a || 0) : (a || 0) - (b || 0)
        );
      }
      if (column === 'Change since last report') {
        sorted.sort(
          ({ changeSinceLastReport: a }, { changeSinceLastReport: b }) => {
            if (a === null || a === undefined) a = 0;
            if (b === null || b === undefined) b = 0;

            return desc ? (b || 0) - (a || 0) : (a || 0) - (b || 0);
          }
        );
      }

      return sorted.map((participant) => ({
        ...participant,
        children: participant.children
          ? recursiveSort(participant.children as ParticipantWithRank[])
          : participant.children,
      }));
    };

    return recursiveSort(participantsFiltered);
  }, [participantsFiltered, sortedColumn]);

  const sortBy = (header: string) => {
    const headerColumn = columns.find((h) => h.label === header);

    if (!headerColumn || !sortableColumns || sortableColumns.length == 0)
      return;

    if (sortedColumn && sortedColumn.column === header) {
      setSortedColumn({ column: headerColumn.label, desc: !sortedColumn.desc });
    }
    // If not sorted by this column yet, start with descending
    else {
      setSortedColumn({ column: headerColumn.label, desc: true });
    }
  };

  if (loading) {
    return (
      <div className="flex h-[540px] flex-col items-center justify-center space-y-3">
        <Spinner size="lg" />
        <Typography className="text-gray-500" variant="text-body-md">
          Loading participants...
        </Typography>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex h-[340px] flex-col items-center justify-center space-y-3">
        <Typography className="text-red-500" variant="text-body-md">
          Error loading report: {error.message}
        </Typography>
      </div>
    );
  }

  return (
    <div className="overflow-x-auto rounded-b-lg">
      <table className="w-full divide-y divide-gray-200">
        <TableHeader
          headers={columns}
          sortBy={sortBy}
          sortableHeaders={sortableColumns}
          sortedColumn={sortedColumn}
          textColor="text-gray-500"
          textVariant="text-button-sm"
        />
        <tbody className="relative z-0 divide-y divide-gray-200 bg-white">
          {participantsSorted.map((participant) => (
            <FullReportRow
              key={participant.id}
              depth={0}
              openLinkContactModal={openLinkContactModal}
              participant={participant}
            />
          ))}
          {participantsSorted.length === 0 && (
            <>
              {filters.length > 0 || (search && search !== '-') ? (
                <TableEmpty
                  clearFilters={() => {
                    setSearch('');
                    setFilters([]);
                  }}
                  columnCount={columns.length}
                  message="No owners found"
                />
              ) : (
                <TableEmpty
                  columnCount={columns.length}
                  message="Report is empty"
                  secondaryMessage="We have not uncovered owners for this report yet."
                />
              )}
            </>
          )}
        </tbody>
        {participantsSorted.length > 0 && (
          <tfoot>
            <tr>
              <td className="p-4 pr-3 lg:pl-3">
                <Typography variant="text-label-sm">Totals</Typography>
              </td>
              <td className="px-3">
                <Typography variant="text-label-sm">{`${totalLayer1Participants} ${translate(
                  'beneficialOwners.accountShort'
                )}s, ${numeral(totalBeneficialOwners).format(
                  '0,0'
                )} ${translate('beneficialOwners.owner')}s`}</Typography>
              </td>
              <td></td>
              <td className="px-3 text-right">
                <Typography variant="text-label-sm">
                  {numeral(totalShares).format('0,0')}
                </Typography>
              </td>
              <td className="px-3 text-right">
                <Typography variant="text-label-sm">
                  {numeral(totalPortion).format('0.00%')}
                </Typography>
              </td>
              <td className="px-3 text-right">
                {totalChange > 0 ? (
                  <Typography className="text-green-600" variant="text-body-sm">
                    {`+ ${numeral(totalChange).format('0,0')}`}
                  </Typography>
                ) : totalChange < 0 ? (
                  <Typography className="text-red-600" variant="text-body-sm">
                    {`${numeral(totalChange).format('0,0')}`}
                  </Typography>
                ) : (
                  <Typography className="text-gray-600" variant="text-body-sm">
                    {`${numeral(totalChange).format('0,0')}`}
                  </Typography>
                )}
              </td>
            </tr>
          </tfoot>
        )}
      </table>
      {linkContactModalData && (
        <LinkContactModal
          currentContact={linkContactModalData.contact || undefined}
          open={!!linkContactModalData}
          redirect={false}
          shareholdingOrAccount={linkContactModalData.shareholdingOrAccount}
          toggleOpen={closeLinkContactModal}
        />
      )}

      {showBackToTop && (
        <Button
          LeadingIcon={() => <ChevronUpIcon height={15} width={15} />}
          className="z-navbar fixed bottom-10 left-1/2 mx-auto hidden w-[95%] -translate-x-1/2 flex-nowrap items-center justify-between gap-3 rounded-lg bg-amber-50 p-3 shadow-lg md:flex md:w-auto"
          size="sm"
          variant="secondary-gray"
          onClick={scrollToTop}
        >
          Back to top
        </Button>
      )}
    </div>
  );
};

export default FullReportTable;
