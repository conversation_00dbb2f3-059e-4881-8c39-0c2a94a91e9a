import { useEffect, useState } from 'react';
import analytics from '@analytics';
import { Badge, Tag, TextInput, TextToggle, Typography } from '@ds';
import { QuestionMarkCircleIcon } from '@heroicons/react/outline';
import { SearchIcon } from '@heroicons/react/solid';
import { useAsyncDebounce } from 'react-table';
import {
  BeneficialOwnerReportDetail,
  BeneficialOwnersOverviewSummary,
} from '@/apollo/generated';
import {
  RENDER_CHANGE_COLUMN,
  SORTABLE_COLUMNS,
  TOP_COLUMNS,
  getHeaderColumns,
  renderHoldingsChange,
  renderFooterSummary,
} from '@/components/investors/beneficial-owners/report/report-pages/columns';
import { useBeneficialOwnersReportsContext } from '@/components/investors/beneficial-owners/report/report-pages/context';
import ExpandableTable, {
  Columns,
} from '@/components/investors/beneficial-owners/report/report-pages/overview/expandable-table';
import {
  groupData,
  renderNumber,
} from '@/components/investors/beneficial-owners/report/report-pages/overview/helpers';
import { Header } from '@/components/utils/tables/table-header';
import { useCurrentCompanyProfileUser } from '@/contexts/current-company-profile-user-context';
import { TRACKING_EVENTS } from '@/utils/tracking-events';

const TAB_NAMES = {
  BY_INVESTOR: 'By Investor',
  FULL_REPORT: 'Full Report',
};

const Top100Table: React.ComponentType = () => {
  const { previousReportDate, search, setSearch, topInvestorsData } =
    useBeneficialOwnersReportsContext();
  const [groupBy, setGroupBy] = useState<string | null>('registeredHolderName');
  const [localSearchTerm, setLocalSearchTerm] = useState(search);
  const [expandedGroups, setExpandedGroups] = useState<{
    [key: string]: boolean;
  }>({});
  const { isUK } = useCurrentCompanyProfileUser();

  useEffect(() => {
    if (
      search &&
      topInvestorsData?.beneficialOwnersTopInvestors
        ?.investorsGroupedByHolders &&
      groupBy
    ) {
      const grouppedData = groupData<BeneficialOwnerReportDetail>(
        topInvestorsData?.beneficialOwnersTopInvestors
          ?.investorsGroupedByHolders,
        groupBy as keyof BeneficialOwnerReportDetail
      );

      const newExpandedGroups = grouppedData.reduce((acc, item) => {
        acc[item.group] = true;
        return acc;
      }, {} as { [key: string]: boolean });
      setExpandedGroups(newExpandedGroups);
    } else {
      setExpandedGroups({});
    }
  }, [
    topInvestorsData?.beneficialOwnersTopInvestors?.investorsGroupedByHolders,
    search,
    groupBy,
  ]);

  const headerColumns: Header[] = [
    {
      className: 'sm:w-[65px] min-w-[65px] whitespace-nowrap',
      label: 'Rank',
      tooltip: {
        children: (
          <QuestionMarkCircleIcon className="h-4 w-4 cursor-pointer text-gray-500" />
        ),
        content: 'Rank by total shares held or managed.',
      },
      value: 'rank',
    },
    {
      className: 'sm:w-[516px] min-w-[242px] whitespace-nowrap',
      label: 'Registered Shareholder',
      tooltip: {
        children: (
          <QuestionMarkCircleIcon className="h-4 w-4 cursor-pointer text-gray-500" />
        ),
        content: 'The account listed on your registry.',
      },
      value: 'investor-name',
    },
    ...(!isUK
      ? [
          {
            className: 'sm:w-[516px] min-w-[135px] whitespace-nowrap',
            label: 'Beneficial Owner',
            tooltip: {
              children: (
                <QuestionMarkCircleIcon className="h-4 w-4 cursor-pointer text-gray-500" />
              ),
              content: 'The underlying owner of the shares.',
            },
            value: 'investor-name',
          },
        ]
      : []),
    {
      className: 'sm:w-[516px] min-w-[135px] whitespace-nowrap',
      label: 'Investor',
      tooltip: {
        children: (
          <QuestionMarkCircleIcon className="h-4 w-4 cursor-pointer text-gray-500" />
        ),
        content: 'The decision-maker or manager of the account.',
      },
      value: 'investment-manager',
    },
    {
      className: 'sm:w-[140px] min-w-[90px] whitespace-nowrap text-right',
      label: 'No. of shares',
      value: 'beneficialOwnerHoldings',
    },
    {
      className: 'sm:w-[140px] min-w-[90px] whitespace-nowrap text-right',
      label: '% of total holdings',
      value: 'percentangeOfHoldings',
    },
    {
      className: 'sm:w-[140px] min-w-[90px] whitespace-nowrap text-right',
      label: 'Change since',
      node: (header, sort) =>
        RENDER_CHANGE_COLUMN(header, sort, previousReportDate),
      value: 'holdingsChange',
    },
    {
      className: 'whitespace-nowrap sm:w-[140px] min-w-[95px] text-right',
      label: 'Location',
      value: 'location',
    },
  ];

  const columns: Columns<BeneficialOwnerReportDetail>[] = [
    {
      field: 'rank',
      groupRenderer: (group: BeneficialOwnerReportDetail[]) => (
        <Typography
          className="text-right text-gray-900"
          variant="text-label-sm"
        >
          {group[0].groupedRank}
        </Typography>
      ),
      itemRenderer: () => <></>,
    },
    {
      field: 'registeredHolderName',
      groupRenderer: (group: BeneficialOwnerReportDetail[]) => (
        <Typography className="grow font-medium" variant="text-label-sm">
          {group[0].registeredHolderName}
          <Badge className="ml-2" color="green" size="sm">
            {`${group?.length || 0}`}
          </Badge>
        </Typography>
      ),
    },
    ...(!isUK
      ? [
          {
            field: 'beneficialOwnerName',
            itemRenderer: (item: BeneficialOwnerReportDetail) => (
              <Typography className="text-gray-500" variant="text-body-sm">
                {item.beneficialOwnerName}
              </Typography>
            ),
          },
        ]
      : []),
    {
      field: 'investmentManagerName',
      itemRenderer: (item: BeneficialOwnerReportDetail) => (
        <Typography className="text-gray-500" variant="text-body-sm">
          {item.investmentManagerName}
        </Typography>
      ),
    },
    {
      field: 'beneficialOwnerHoldings',
      groupRenderer: (group: BeneficialOwnerReportDetail[]) => (
        <Typography
          className="text-right text-gray-900"
          variant="text-label-sm"
        >
          {renderNumber(group[0].groupedHoldingsSum || 0)}
        </Typography>
      ),
      itemRenderer: (item: BeneficialOwnerReportDetail) => (
        <Typography
          className="pr-2 text-right text-gray-500"
          variant="text-body-sm"
        >
          {renderNumber(item.beneficialOwnerHoldings || 0)}
        </Typography>
      ),
    },
    {
      field: 'percentangeOfHoldings',
      groupRenderer: (group: BeneficialOwnerReportDetail[]) => (
        <Typography
          className="pr-2 text-right text-gray-900"
          variant="text-label-sm"
        >
          {renderNumber(group[0].groupedHoldingsPercentage || 0, true)}%
        </Typography>
      ),
      itemRenderer: (item: BeneficialOwnerReportDetail) => (
        <Typography
          className="pr-2 text-right text-gray-500"
          variant="text-body-sm"
        >
          {renderNumber(item.percentangeOfHoldings || 0, true)}%
        </Typography>
      ),
    },
    {
      field: 'holdingsChange',
      groupRenderer: (group: BeneficialOwnerReportDetail[]) => (
        <>
          {group[0].groupedHoldingsChange ? (
            <>{renderHoldingsChange(group[0].groupedHoldingsChange)}</>
          ) : group[0].hasPastHoldings ? (
            <>{renderHoldingsChange(group[0].holdingsChange || 0)}</>
          ) : (
            <Typography
              className="pr-1 text-center text-gray-500"
              variant="text-body-sm"
            >
              N/A
            </Typography>
          )}
        </>
      ),
      itemRenderer: (item: BeneficialOwnerReportDetail) => (
        <>
          {item.hasPastHoldings && (
            <>{renderHoldingsChange(item.holdingsChange || 0)}</>
          )}
          {!item.hasPastHoldings && (
            <Typography
              className="pr-1 text-center text-gray-500"
              variant="text-body-sm"
            >
              N/A
            </Typography>
          )}
        </>
      ),
    },
    {
      field: 'beneficialOwnerState',
      itemRenderer: (item: BeneficialOwnerReportDetail) => (
        <div className="pr-2 text-right">
          {item.beneficialOwnerState && (
            <>
              <Tag color="violet" size="sm">
                {item.beneficialOwnerState}
              </Tag>
              <br />
            </>
          )}
          {item.beneficialOwnerCountry && (
            <Tag color="moss" size="sm">
              {item.beneficialOwnerCountry}
            </Tag>
          )}
        </div>
      ),
    },
  ];

  const renderFooter = (summary?: BeneficialOwnersOverviewSummary) => {
    const commonClasses = 'text-right font-medium text-gray-900';
    const commonTd = 'table-cell h-[55px] p-3';

    return (
      <tfoot className="border-t-2 border-gray-200">
        <tr className="bg-white">
          <td className={commonTd}>
            <Typography className="pr-2 text-gray-900" variant="text-label-sm">
              Totals
            </Typography>
          </td>
          <td className={commonTd}>
            <Typography
              className="text-left font-medium text-gray-900"
              variant="text-label-sm"
            >
              {renderNumber(summary?.registeredHolder || 0)} accounts
            </Typography>
          </td>
          {!isUK && (
            <td className={commonTd}>
              <Typography
                className="text-left font-medium text-gray-900"
                variant="text-label-sm"
              >
                {renderNumber(summary?.beneficialOwner || 0)} owners
              </Typography>
            </td>
          )}

          <td className={commonTd}>
            <Typography
              className="text-left font-medium text-gray-900"
              variant="text-label-sm"
            >
              {renderNumber(summary?.investmentManager || 0)} investors
            </Typography>
          </td>
          <td className={commonTd}>
            <Typography className={commonClasses} variant="text-label-sm">
              {renderNumber(summary?.beneficialOwnerHoldings || 0)}
            </Typography>
          </td>
          <td className={commonTd}>
            <Typography className={commonClasses} variant="text-label-sm">
              {renderNumber(summary?.percentangeOfHoldings || 0, true)}%
            </Typography>
          </td>
          <td className={commonTd}>
            <Typography className={commonClasses} variant="text-label-sm">
              {summary?.hasPastHoldings
                ? renderNumber(summary?.holdingsChange || 0)
                : 'N/A'}
            </Typography>
          </td>
          <td className={commonTd} />
        </tr>
      </tfoot>
    );
  };

  const setDebouncedSearch = useAsyncDebounce((value) => {
    setSearch(value);
  }, 250);

  return (
    <>
      <TextInput
        autoFocus
        LeadingIcon={SearchIcon}
        aria-placeholder="Search by registered shareholder, beneficial owner, or investor"
        className="w-full rounded-lg"
        placeholder="Search by registered shareholder, beneficial owner, or investor"
        type="search"
        value={localSearchTerm}
        onChange={(e) => {
          analytics.track(
            TRACKING_EVENTS.beneficialOwners.beneficialOwnerSearchUsed
          );

          setLocalSearchTerm(e.target.value);
          setDebouncedSearch(e.target.value);
        }}
      />
      <ExpandableTable<BeneficialOwnerReportDetail>
        columns={groupBy == null ? TOP_COLUMNS : columns}
        dataProvider={
          groupBy == null
            ? topInvestorsData?.beneficialOwnersTopInvestors?.topInvestors
            : topInvestorsData?.beneficialOwnersTopInvestors
                ?.investorsGroupedByHolders
        }
        defaultSortColumn={{ column: 'No. of shares', desc: true }}
        emptyRecordButton={() => {
          setLocalSearchTerm('');
          setDebouncedSearch('');
        }}
        expandedGroups={expandedGroups}
        extraHeaderRenderer={() => {
          return (
            <TextToggle
              items={[
                {
                  label: TAB_NAMES.FULL_REPORT,
                  onClick: () => {
                    analytics.track(
                      TRACKING_EVENTS.beneficialOwners
                        .beneficialOwnerTopHoldingsSwitcherClicked,
                      { item: TAB_NAMES.FULL_REPORT }
                    );

                    setGroupBy('registeredHolderName');
                  },
                },
                {
                  label: TAB_NAMES.BY_INVESTOR,
                  onClick: () => {
                    analytics.track(
                      TRACKING_EVENTS.beneficialOwners
                        .beneficialOwnerTopHoldingsSwitcherClicked,
                      { item: TAB_NAMES.BY_INVESTOR }
                    );

                    setGroupBy(null);
                  },
                },
              ]}
            />
          );
        }}
        footerRenderer={() => {
          return groupBy == null
            ? renderFooterSummary(
                topInvestorsData?.beneficialOwnersTopInvestors?.topSummary ||
                  undefined
              )
            : renderFooter(
                topInvestorsData?.beneficialOwnersTopInvestors?.allSummary ||
                  undefined
              );
        }}
        groupBy={groupBy}
        headerColumns={
          groupBy == null ? getHeaderColumns(previousReportDate) : headerColumns
        }
        sortableColumns={SORTABLE_COLUMNS}
        subtitle="Your key investors by total shares held and/or managed."
        title="Top holdings broken down by investor"
      />
    </>
  );
};

export default Top100Table;
