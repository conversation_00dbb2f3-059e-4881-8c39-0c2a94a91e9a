import { useCallback, useState, useEffect } from 'react';
import { <PERSON><PERSON>, Button, Typography } from '@ds';
import { ChevronUpIcon, ChevronDownIcon } from '@heroicons/react/outline';
import clsx from 'clsx';
import {
  groupData,
  renderNumber,
} from '@/components/investors/beneficial-owners/report/report-pages/overview/helpers';
import useBreakpoint from '@/components/pdf-viewer/use-breakpoint';
import TableEmpty from '@/components/utils/tables/empty';
import TableHeader, { Header } from '@/components/utils/tables/table-header';

export interface Columns<T> {
  field: string;
  groupRenderer?: (items: T[]) => JSX.Element;
  itemRenderer?: (item: T) => JSX.Element;
  labelFunction?: (item: T) => string;
}

interface Props<T> {
  columns: Columns<T>[];
  dataProvider?: T[] | null;
  defaultSortColumn?: { column?: string; desc: boolean };
  emptyRecordButton?: () => void;
  expandedGroups?: { [key: string]: boolean };
  extraHeaderRenderer?: () => JSX.Element;
  footerRenderer?: () => JSX.Element;
  groupBy?: string | null;
  headerColumns: Header[];
  rowCount?: number;
  showRowCount?: boolean;
  sortableColumns: string[];
  subtitle?: string;
  title: string;
  viewMore?: boolean;
}

// https://www.typescriptlang.org/docs/handbook/advanced-types.html#index-types
function getProperty<T, K extends keyof T>(o: T, propertyName: K): T[K] {
  return o[propertyName]; // o[propertyName] is of type T[K]
}

function sumBy<T>(data: T[], field?: keyof T): number {
  return data.reduce((sum, item) => {
    const value = field ? getProperty(item, field) : item;
    return sum + (typeof value === 'number' ? value : 0);
  }, 0);
}

const ExpandableTable = <T,>({
  columns,
  dataProvider,
  defaultSortColumn,
  emptyRecordButton,
  expandedGroups,
  extraHeaderRenderer,
  footerRenderer,
  groupBy,
  headerColumns,
  rowCount,
  showRowCount = false,
  sortableColumns,
  subtitle,
  title,
  viewMore = true,
}: Props<T>) => {
  const breakPoint = useBreakpoint() ?? '';

  const [isViewMore, toggleViewMore] = useState(false);
  const [sortedColumn, setSortedColumn] = useState(defaultSortColumn);
  const [internalExpandedGroups, setInternalExpandedGroups] = useState<{
    [key: string]: boolean;
  }>({});

  useEffect(() => {
    if (expandedGroups) {
      setInternalExpandedGroups(expandedGroups);
    }
  }, [expandedGroups]);

  const onClickShowMore = () => {
    toggleViewMore(!isViewMore);
  };

  const toggleGroup = (group: string) => {
    setInternalExpandedGroups((prev) => ({
      ...prev,
      [group]: !prev[group],
    }));
  };

  const sortBy = (header: string) => {
    const headerColumn = headerColumns.find((h) => h.label === header);

    if (!headerColumn || !sortableColumns || sortableColumns.length == 0)
      return;

    if (sortedColumn && sortedColumn.column === header) {
      // If already sorted descending, switch to ascending
      if (sortedColumn.desc) {
        setSortedColumn({ column: headerColumn.label, desc: false });
      }
      // If already sorted ascending, remove sorting
      else {
        setSortedColumn(undefined);
      }
    }
    // If not sorted by this column yet, start with descending
    else {
      setSortedColumn({ column: headerColumn.label, desc: true });
    }
  };

  const renderCell = useCallback((item: T | T[], column: Columns<T>) => {
    if (item instanceof Array) {
      if (column.groupRenderer) {
        return column.groupRenderer(item);
      }
      return '';
    }
    const value = item[column.field as keyof T] as T;
    let formattedValue = value as string;

    if (column.itemRenderer) {
      return column.itemRenderer(item);
    }

    if (column.labelFunction) {
      formattedValue = column.labelFunction(value);
    }

    return (
      <Typography variant="text-label-md">
        <Typography>{formattedValue}</Typography>
      </Typography>
    );
  }, []);

  const getSortedData = useCallback(
    (data: T[]) => {
      if (!sortedColumn?.column) return data;

      const column = headerColumns.find((h) => h.label === sortedColumn.column);

      if (!column) return data;

      return [...data].sort((a, b) => {
        const aValue = a[column.value as keyof T];
        const bValue = b[column.value as keyof T];

        const aNum =
          typeof aValue === 'number'
            ? Math.abs(aValue)
            : Math.abs(Number(aValue)) || 0;
        const bNum =
          typeof bValue === 'number'
            ? Math.abs(bValue)
            : Math.abs(Number(bValue)) || 0;

        if (aNum < bNum) return sortedColumn.desc ? 1 : -1;
        if (aNum > bNum) return sortedColumn.desc ? -1 : 1;
        return 0;
      });
    },
    [sortedColumn, headerColumns]
  );

  const getGroupSortData = useCallback(
    (data: { group: string; values: T[] }[]) => {
      if (!sortedColumn?.column) return data;

      const column = headerColumns.find((h) => h.label === sortedColumn.column);

      if (!column) return data;

      return [...data].sort((a, b) => {
        const aValue = sumBy<T>(a.values, column.value as keyof T);
        const bValue = sumBy<T>(b.values, column.value as keyof T);

        const aNum = typeof aValue === 'number' ? aValue : aValue || 0;
        const bNum = typeof bValue === 'number' ? bValue : bValue || 0;

        if (aNum < bNum) return sortedColumn.desc ? 1 : -1;
        if (aNum > bNum) return sortedColumn.desc ? -1 : 1;
        return 0;
      });
    },
    [sortedColumn, headerColumns]
  );

  const renderContent = useCallback(() => {
    if (!dataProvider) return null;

    const sortedData = getSortedData(dataProvider);
    const displayData =
      viewMore && rowCount && !isViewMore
        ? sortedData.slice(0, rowCount)
        : sortedData;

    return displayData.map((item, index) => {
      return (
        <tr
          key={index}
          className={
            'table-row border-b bg-white first:border-t last:border-0 md:first:border-t-0'
          }
        >
          {columns.map((column, colIndex) => (
            <td
              key={colIndex}
              className="table-cell h-[55px] p-3 first:lg:pl-4 first:lg:pr-0"
            >
              {renderCell(item, column)}
            </td>
          ))}
        </tr>
      );
    });
  }, [
    dataProvider,
    columns,
    renderCell,
    rowCount,
    isViewMore,
    viewMore,
    getSortedData,
  ]);

  const renderGroup = useCallback(() => {
    if (!dataProvider || !groupBy) return null;

    const sortedData = getSortedData(dataProvider);

    const groupedData = groupData<T>(sortedData, groupBy as keyof T);

    const sortedGroupedData = getGroupSortData(groupedData);

    const displayData =
      viewMore && rowCount && !isViewMore
        ? sortedGroupedData.slice(0, rowCount)
        : sortedGroupedData;

    return displayData.map(({ group, values }, groupIndex) => {
      const isExpanded = internalExpandedGroups[group];

      const aggregatedRow = (
        <tr
          key={`group-${group}`}
          className="table-row cursor-pointer border-b first:border-t last:border-0 hover:bg-gray-50 md:first:border-t-0"
          onClick={() => toggleGroup(group)}
        >
          {columns.map((column, colIndex) => {
            if (colIndex === 0) {
              return (
                <td key={colIndex} className="p-3 lg:pl-4 lg:pr-0">
                  <div className="flex items-center">
                    {isExpanded ? (
                      <ChevronUpIcon className="mr-2 h-4 w-4" />
                    ) : (
                      <ChevronDownIcon className="mr-2 h-4 w-4" />
                    )}
                    <Typography className="text-gray-500">
                      {renderCell(values, column)}
                    </Typography>
                  </div>
                </td>
              );
            }

            return (
              <td
                key={colIndex}
                className="table-cell h-[55px] p-3 font-semibold"
              >
                {renderCell(values, column)}
              </td>
            );
          })}
        </tr>
      );

      const itemRows = isExpanded
        ? values.map((item, index) => (
            <tr
              key={`${group}-${index}`}
              className="table-row border-b bg-white first:border-t last:border-0 md:first:border-t-0"
            >
              {columns.map((column, colIndex) => (
                <td key={colIndex} className="table-cell h-[55px] p-3">
                  {column.field !== groupBy ? renderCell(item, column) : ''}
                </td>
              ))}
            </tr>
          ))
        : [];

      return [aggregatedRow, ...itemRows];
    });
  }, [
    dataProvider,
    groupBy,
    columns,
    renderCell,
    rowCount,
    isViewMore,
    viewMore,
    getSortedData,
    getGroupSortData,
    internalExpandedGroups,
  ]);

  const showViewMoreButton =
    viewMore && rowCount && dataProvider && dataProvider.length > rowCount;

  const maxHeight = ((rowCount || 6) + 1) * 55;

  return (
    <div
      data-private
      className={clsx(
        `min-w-full rounded-lg border border-gray-200 bg-white`,
        !['3xl'].includes(breakPoint) && 'overflow-hidden'
      )}
    >
      {/* TODO: we can also make the header sticky, although it's probably better to move this into the header component so that the parent is within the table so the column headers and the title have the same bottom boundary */}
      <div className="rounded-lg bg-white px-3 py-4">
        <div className="flex items-center justify-between">
          <div>
            <div className="flex items-center gap-2">
              <Typography variant="text-heading-sm">{title}</Typography>
              {showRowCount && (
                <Badge color="gray" size="sm">
                  {renderNumber(dataProvider?.length || 0)}
                </Badge>
              )}
            </div>
            {subtitle && (
              <Typography className="text-gray-500" variant="text-body-sm">
                {subtitle}
              </Typography>
            )}
          </div>
          {extraHeaderRenderer && extraHeaderRenderer()}
        </div>
      </div>

      <div className="w-full border-b border-b-gray-200" />
      <div
        className={clsx(
          !['3xl'].includes(breakPoint) && 'overflow-x-auto',
          !['3xl'].includes(breakPoint) && !viewMore ? 'overflow-y-auto' : ''
        )}
        style={!viewMore ? { maxHeight: `${maxHeight}px` } : undefined}
      >
        {dataProvider?.length === 0 && (
          <div className="grid place-items-center bg-gray-50 p-4">
            <table className="table min-w-full border-t border-t-gray-200 lg:border-t-0">
              <tbody>
                <TableEmpty
                  clearFilters={emptyRecordButton}
                  customClearText="Clear search"
                  message="No data found"
                  secondaryMessage="Your search did not match any results, please try again!"
                />
              </tbody>
            </table>
          </div>
        )}

        {dataProvider && dataProvider?.length > 0 && (
          <table className="table min-w-full border-t border-t-gray-200 lg:border-t-0">
            <TableHeader
              className="z-tooltip sticky top-0 rounded-lg bg-white shadow-sm shadow-gray-200"
              headers={headerColumns}
              sortBy={sortBy}
              sortableHeaders={sortableColumns}
              sortedColumn={sortedColumn}
              textColor="text-gray-500"
              textVariant="text-button-sm"
            />
            <tbody className="table-row-group">
              {groupBy ? renderGroup() : renderContent()}
            </tbody>
            {footerRenderer && footerRenderer()}
          </table>
        )}
      </div>

      {showViewMoreButton && (
        <div className="flex w-full items-center justify-center border-t border-t-gray-200 p-4">
          <Button
            TrailingIcon={isViewMore ? ChevronUpIcon : ChevronDownIcon}
            variant="secondary-gray"
            onClick={() => onClickShowMore()}
          >
            {isViewMore ? 'Show less' : 'View more'}
          </Button>
        </div>
      )}
    </div>
  );
};

export default ExpandableTable;
