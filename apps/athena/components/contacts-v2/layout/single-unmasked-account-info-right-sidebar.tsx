import React, {
  ReactElement,
  SVGProps,
  useCallback,
  useMemo,
  useState,
} from 'react';
import analytics from '@analytics';
import { Skeleton, TextInput, Badge, Button, Typography } from '@ds';
import {
  ArrowDownIcon,
  ArrowRightIcon,
  ArrowUpIcon,
  CalendarIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  ScaleIcon,
  UsersIcon,
  XIcon,
  HashtagIcon,
} from '@heroicons/react/outline';
import {
  ArrowUpRightIcon,
  GlobeAmericasIcon,
  MagnifyingGlassIcon,
  MapPinIcon,
} from '@heroicons/react-v2/24/outline';
import clsx from 'clsx';
import dayjs from 'dayjs';
import Link from 'next/link';
import { useRouter } from 'next/router';
import {
  useBeneficialOwnersAccountsByHoldingIdsWithLatestHoldingQuery,
  BeneficialOwnerAggregateHolding,
  useContactBeneficialOwnerHoldingsSummaryQuery,
} from '@/apollo/generated';
import { useContactProfileContext } from '@/components/contacts-v2/contacts-context';
import { useCurrentCompanyProfileUser } from '@/contexts/current-company-profile-user-context';
import { formatMoney } from '@/utils/common-helpers';
import routes from '@/utils/routes';
import { TRACKING_EVENTS } from '@/utils/tracking-events';

const SingleUnmaskedAccountInfoRightSidebar: React.ComponentType = () => {
  const {
    currentCompanyProfileUser: {
      profile: {
        currency,
        ticker: { marketListingKey },
      },
    },
    isUK,
    price,
  } = useCurrentCompanyProfileUser();

  const {
    beneficialOwnerDataForSidebar: account,
    contact,
    toggleOpenLinkContactModal,
  } = useContactProfileContext();

  const { pathname, push, query } = useRouter();

  const [showRetailOwners, setShowRetailOwners] = useState(false);
  const [isAtTop, setIsAtTop] = useState(true);
  const [isAtBottom, setIsAtBottom] = useState(false);
  const [isAtTopTransactions, setIsAtTopTransactions] = useState(true);
  const [isAtBottomTransactions, setIsAtBottomTransactions] = useState(false);
  const [showTransactions, setShowTransactions] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const target = e.target as HTMLDivElement;
    const isTop = target.scrollTop === 0;
    const isBottom =
      Math.abs(target.scrollHeight - target.scrollTop - target.clientHeight) <
      1;

    setIsAtTop(isTop);
    setIsAtBottom(isBottom);
  };

  const childrenLatestHoldingIds = useMemo(() => {
    if (
      !account ||
      !account?.latestHolding ||
      !account?.latestHolding?.children
    )
      return [];
    return account?.latestHolding?.children.map((child) => child?.id);
  }, [account]);

  const { data: shareholdingSummaryData, loading: shareholdingSummaryLoading } =
    useContactBeneficialOwnerHoldingsSummaryQuery({
      fetchPolicy: 'network-only',
      skip: !contact?.id || !account?.id,
      variables: {
        accountId: account?.id || '',
        contactId: contact?.id,
        endDate: dayjs().format('YYYY-MM-DD'),
        startDate: '1900-01-01',
      },
    });

  const movementsData = useMemo(() => {
    if (
      !shareholdingSummaryData?.contactBeneficialOwnerHoldingsSummary
        ?.beneficialOwnerHoldings
    )
      return [];
    return shareholdingSummaryData.contactBeneficialOwnerHoldingsSummary.beneficialOwnerHoldings.map(
      (b, index, array) => {
        const previousBalance =
          index < array.length - 1 ? array[index + 1]?.balance || 0 : 0;
        const currentBalance = b?.balance || 0;
        const movement = currentBalance - previousBalance;

        return {
          ...b,
          movement,
        };
      }
    );
  }, [shareholdingSummaryData]);

  const { data, loading: latestHoldingLoading } =
    useBeneficialOwnersAccountsByHoldingIdsWithLatestHoldingQuery({
      skip:
        !childrenLatestHoldingIds.length ||
        childrenLatestHoldingIds.length === 0,
      variables: {
        holdingIds: account?.latestHolding?.children?.map(
          (h) => h?.id
        ) as string[],
      },
    });

  const children = useMemo(() => {
    if (!data) return [];

    if (searchQuery)
      return data?.beneficialOwnerAccountsByHoldingIdsWithLatestHolding?.filter(
        (b) => b.accountName.toLowerCase().includes(searchQuery.toLowerCase())
      );

    return data?.beneficialOwnerAccountsByHoldingIdsWithLatestHolding;
  }, [data, searchQuery]);

  const handleScrollTransactions = (e: React.UIEvent<HTMLDivElement>) => {
    const target = e.target as HTMLDivElement;
    const isTop = target.scrollTop === 0;
    const isBottom =
      Math.abs(target.scrollHeight - target.scrollTop - target.clientHeight) <
      1;

    setIsAtTopTransactions(isTop);
    setIsAtBottomTransactions(isBottom);
  };

  const renderHoldings = useCallback(() => {
    if (!account) return null;
    const shares = account?.latestHolding?.shares;

    return (
      <Row LeadingIcon={ScaleIcon} label="Holdings">
        <div className="flex items-start gap-2">
          <Typography variant="text-label-sm">
            {shares ? shares?.toLocaleString() : 0}
          </Typography>
          <Typography className="text-wrap break-all" variant="text-body-sm">
            ({formatMoney(price && shares ? shares * price : 0, currency ?? '')}
            )
          </Typography>
        </div>
      </Row>
    );
  }, [account, price, currency]);

  const renderDepot = useCallback(() => {
    if (!isUK) return null;
    return (
      <Row LeadingIcon={HashtagIcon} label="Depot">
        <Typography variant="text-label-sm">{account?.depot}</Typography>
      </Row>
    );
  }, [account?.depot, isUK]);

  const renderLastUpdated = useCallback(() => {
    return (
      <Row LeadingIcon={CalendarIcon} label="Last updated">
        <div>
          <Typography variant="text-label-sm">
            {dayjs(account?.latestHolding?.report?.reportDate).format(
              'DD MMM YY'
            )}
            {movementsData && movementsData.length > 0 && (
              <Typography variant="text-label-sm">
                ({movementsData[0].movement > 0 ? '+' : ''}
                {movementsData[0].movement?.toLocaleString()})
              </Typography>
            )}
          </Typography>
          <Link
            className="text-amplify-green-700"
            href={routes?.investors?.beneficialOwners?.reportTwo?.href(
              marketListingKey as string,
              account?.latestHolding?.report?.id as string,
              `search=${account?.accountName}`
            )}
            target="_blank"
          >
            <div className="flex items-center gap-2 text-nowrap underline">
              <Typography variant="text-label-sm">See latest report</Typography>
              <ArrowUpRightIcon className="text-amplify-green-700 h-4 w-4" />
            </div>
          </Link>
        </div>
      </Row>
    );
  }, [
    account?.latestHolding?.report?.reportDate,
    account?.latestHolding?.report?.id,
    account?.accountName,
    movementsData,
    marketListingKey,
  ]);

  const renderHeldUnder = useCallback(() => {
    if (
      !account?.latestHolding?.parent &&
      !account?.latestHolding?.shareholding
    )
      return null;

    const accountName = account?.latestHolding?.parent
      ? account?.latestHolding?.parent?.beneficialOwnerAccount?.accountName
      : account?.latestHolding?.shareholding?.accountName;

    const urlPath = account?.latestHolding?.parent
      ? routes?.investors?.search?.contacts?.contact?.href(
          marketListingKey,
          account?.latestHolding?.parent?.beneficialOwnerAccount?.contact
            ?.id as string
        )
      : routes?.investors?.search?.shareholders?.shareholder?.href(
          marketListingKey,
          account?.latestHolding?.shareholding?.id as string
        );
    return (
      <Row LeadingIcon={UsersIcon} label="Held under">
        <Link className="text-amplify-green-700" href={urlPath} target="_blank">
          <div className="flex items-center gap-2 text-wrap underline">
            <Typography variant="text-label-sm">{accountName}</Typography>
            <ArrowUpRightIcon className="text-amplify-green-700 h-4 w-4" />
          </div>
        </Link>
      </Row>
    );
  }, [
    marketListingKey,
    account?.latestHolding?.parent,
    account?.latestHolding?.shareholding,
  ]);

  const renderContent = useMemo(() => {
    return (
      <div className="relative h-full w-full max-w-[320px] space-y-4">
        <div className="flex items-center justify-between">
          <Typography variant="text-heading-md">
            {account?.accountName}
          </Typography>
          <Button
            size="sm"
            variant="link-gray"
            onClick={() => {
              const { accountId, shareholdingId, ...rest } = query;
              push(
                {
                  pathname,
                  query: {
                    ...rest,
                  },
                },
                undefined,
                {
                  shallow: true,
                }
              );

              analytics.track(
                TRACKING_EVENTS.contactProfile
                  .portfolioUnmaskedAccountSidePanelClosed
              );
            }}
          >
            <XIcon className="h-4 w-4" />
          </Button>
        </div>
        <Badge color="moss" size="sm">
          {childrenLatestHoldingIds.length > 0
            ? 'Intermediary owner'
            : 'Beneficial owner'}
        </Badge>
        {/* Shareholding info */}
        <section className="bg-gray-25 rounded-lg border p-4">
          <div className="space-y-4 pb-2">
            {renderHoldings()}
            {renderLastUpdated()}
            {renderDepot()}
            {account?.addressState && (
              <Row
                LeadingIcon={() => (
                  <MapPinIcon className="h-4 w-4 min-w-[16px]" />
                )}
                label={isUK ? 'County' : 'State'}
              >
                <Typography variant="text-label-sm">
                  {account?.addressState}
                </Typography>
              </Row>
            )}
            {account?.addressCountry && (
              <Row
                LeadingIcon={() => (
                  <GlobeAmericasIcon className="h-4 w-4 min-w-[16px]" />
                )}
                label="Country"
              >
                <Typography variant="text-label-sm">
                  {account?.addressCountry}
                </Typography>
              </Row>
            )}
            {renderHeldUnder()}
          </div>
        </section>

        {children && children?.length > 0 && (
          <>
            <div className="ml-[-24px] h-px w-[calc(100%+48px)] bg-gray-200" />
            <section className="z-10">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Typography variant="text-heading-sm">
                    Unmasked accounts
                  </Typography>
                  <Badge>
                    {children?.length}
                    {(children && children?.length > 1) ||
                    children?.length === 0
                      ? ' accounts'
                      : ' account'}
                  </Badge>
                </div>
                <div
                  className={clsx(
                    `ml-2 cursor-pointer transition-transform duration-300`
                  )}
                  onClick={() => setShowRetailOwners(!showRetailOwners)}
                >
                  {showRetailOwners ? (
                    <ChevronUpIcon className="h-4 w-4" />
                  ) : (
                    <ChevronDownIcon className="h-4 w-4" />
                  )}
                </div>
              </div>
              <div
                className={`relative transition-[max-height,margin-top,opacity] duration-300 ease-in-out ${
                  showRetailOwners
                    ? 'mt-4 max-h-[480px] opacity-100'
                    : 'pointer-events-none mt-0 max-h-0 opacity-0'
                }`}
              >
                <TextInput
                  LeadingIcon={MagnifyingGlassIcon}
                  placeholder="Search account name"
                  size="sm"
                  onChange={(e) => {
                    setSearchQuery(e.target.value);
                  }}
                />
                <div className="mb-2 mt-4 h-px w-full bg-gray-200" />

                <div
                  className="space-y-4 overflow-y-auto pr-1"
                  style={{ maxHeight: '420px' }}
                  onScroll={handleScroll}
                >
                  {children?.map((b) => {
                    if (!b) return null;

                    return (
                      <div
                        key={b?.id}
                        className="space-y-2 border-b border-b-gray-200 pb-3 last:border-0"
                      >
                        <div className="flex items-center justify-between">
                          <Typography variant="text-label-sm">
                            {b?.accountName}
                          </Typography>
                          <Link
                            className="text-amplify-green-600"
                            href={routes?.investors?.search?.contacts?.contact?.href(
                              marketListingKey,
                              b?.latestHolding?.beneficialOwnerAccount?.contact
                                ?.id as string
                            )}
                          >
                            <div className="border-b-amplify-green-600 flex items-center gap-2 hover:border-b">
                              <Typography variant="text-button-sm">
                                View
                              </Typography>
                              <ArrowRightIcon className="h-4 w-4" />
                            </div>
                          </Link>
                        </div>
                        <Badge color="moss" size="sm">
                          {b?.latestHolding &&
                          b?.latestHolding?.children &&
                          b?.latestHolding?.children.length > 0
                            ? 'Intermediary'
                            : 'Retail holder'}
                        </Badge>
                        <Row LeadingIcon={ScaleIcon} label="Holdings">
                          <div>
                            <Typography variant="text-body-sm">
                              {(b?.latestHolding?.shares ?? 0).toLocaleString()}{' '}
                              {(b?.latestHolding &&
                                b?.latestHolding?.shares > 0) ||
                              b?.latestHolding?.shares === 0
                                ? 'shares'
                                : 'share'}
                            </Typography>
                            <Typography
                              className="text-gray-500"
                              variant="text-body-sm"
                            >
                              Last updated:{' '}
                              {dayjs(b?.latestHolding?.updatedAt).format(
                                'DD MMM YY'
                              )}
                            </Typography>
                          </div>
                        </Row>
                      </div>
                    );
                  })}
                  {showRetailOwners &&
                    !!children?.length &&
                    children?.length > 2 && (
                      <>
                        <div
                          className={`pointer-events-none absolute inset-x-0 bottom-0 h-12 bg-gradient-to-t from-white to-transparent transition-opacity duration-300 ${
                            isAtBottom ? 'opacity-0' : 'opacity-100'
                          }`}
                        />
                        <div
                          className={`pointer-events-none absolute inset-x-0 top-10 h-12 bg-gradient-to-b from-white to-transparent transition-opacity duration-300 ${
                            isAtTop ? 'opacity-0' : 'opacity-100'
                          }`}
                        />
                      </>
                    )}
                </div>
              </div>
              {latestHoldingLoading && (
                <div className="space-y-4">
                  <Skeleton loading height={100} variant="rect" />
                  <Skeleton loading height={100} variant="rect" />
                  <Skeleton loading height={100} variant="rect" />
                </div>
              )}
            </section>
          </>
        )}
        <div className="ml-[-24px] h-px w-[calc(100%+48px)] bg-gray-200" />
        <section className="z-10">
          <div className="flex items-center justify-between">
            <Typography variant="text-heading-sm">Movements</Typography>
            <div
              className={clsx(
                `ml-2 cursor-pointer transition-transform duration-300`,
                shareholdingSummaryLoading && 'hidden'
              )}
              onClick={() => setShowTransactions(!showTransactions)}
            >
              {showTransactions ? (
                <ChevronUpIcon className="h-4 w-4" />
              ) : (
                <ChevronDownIcon className="h-4 w-4" />
              )}
            </div>
          </div>
          <div
            className={`relative transition-[max-height,margin-top,opacity] duration-300 ease-in-out ${
              showTransactions
                ? 'mt-4 max-h-[480px] opacity-100'
                : 'mt-0 max-h-0 opacity-0'
            }`}
          >
            <div
              className="space-y-4 overflow-y-auto pr-1"
              style={{ maxHeight: '420px' }}
              onScroll={handleScrollTransactions}
            >
              {movementsData?.map((movement) => {
                if (!movement) return null;
                return (
                  <TransactionItem
                    key={movement?.id}
                    movement={
                      movement as BeneficialOwnerAggregateHolding & {
                        movement: number;
                      }
                    }
                  />
                );
              })}
            </div>
            {showTransactions &&
              movementsData?.length &&
              movementsData?.length > 5 && (
                <>
                  <div
                    className={`pointer-events-none absolute inset-x-0 bottom-0 h-12 bg-gradient-to-t from-white to-transparent transition-opacity duration-300 ${
                      isAtBottomTransactions ? 'opacity-0' : 'opacity-100'
                    }`}
                  />
                  <div
                    className={`pointer-events-none absolute inset-x-0 top-0 h-12 bg-gradient-to-b from-white to-transparent transition-opacity duration-300 ${
                      isAtTopTransactions ? 'opacity-0' : 'opacity-100'
                    }`}
                  />
                </>
              )}
          </div>
          {shareholdingSummaryLoading && (
            <div className="space-y-4">
              <Skeleton loading height={20} />
              <Skeleton loading height={20} />
            </div>
          )}
        </section>
        <div className="ml-[-24px] h-px w-[calc(100%+48px)] bg-gray-200" />
        <section className="relative z-20">
          <Button
            destructive
            className="w-full text-gray-700"
            size="md"
            variant="secondary-gray"
            onClick={() => toggleOpenLinkContactModal(true)}
          >
            Relink account
          </Button>
        </section>
      </div>
    );
  }, [
    account?.accountName,
    account?.addressState,
    account?.addressCountry,
    childrenLatestHoldingIds.length,
    renderHoldings,
    renderLastUpdated,
    renderHeldUnder,
    isUK,
    pathname,
    push,
    query,
    renderDepot,
    children,
    showRetailOwners,
    isAtBottom,
    isAtTop,
    latestHoldingLoading,
    shareholdingSummaryLoading,
    showTransactions,
    movementsData,
    isAtBottomTransactions,
    isAtTopTransactions,
    marketListingKey,
    toggleOpenLinkContactModal,
  ]);

  return (
    <div className="h-fit overflow-y-clip rounded-lg border border-gray-200 bg-white p-4">
      {renderContent}
    </div>
  );
};

export default SingleUnmaskedAccountInfoRightSidebar;

const Row = ({
  LeadingIcon,
  children,
  label,
}: {
  LeadingIcon:
    | ((props: SVGProps<SVGSVGElement>) => ReactElement | null)
    | React.ForwardRefExoticComponent<
        Omit<React.SVGProps<SVGSVGElement>, 'ref'> & {
          title?: string | undefined;
          titleId?: string | undefined;
        } & React.RefAttributes<SVGSVGElement>
      >;
  children: React.ReactNode;
  label: string;
}) => {
  return (
    <div className="grid grid-cols-7 items-start gap-4">
      <div className="col-span-3 flex items-center gap-1 text-gray-600">
        {LeadingIcon && <LeadingIcon className="h-4 w-4 min-w-[16px]" />}
        <Typography variant="text-body-sm">{label}</Typography>
      </div>
      <div className="col-span-4">{children}</div>
    </div>
  );
};

interface TransactionItemProps {
  movement: BeneficialOwnerAggregateHolding & {
    movement: number;
  };
}
const TransactionItem = ({ movement }: TransactionItemProps) => {
  const renderMovementType = () => {
    if (movement?.movement > 0) {
      return (
        <div className="flex items-center gap-2">
          <Badge color="green" size="sm">
            <div className="flex items-center gap-1">
              <ArrowUpIcon className="h-3 w-3" />
              <Typography variant="text-caption">Bought</Typography>
            </div>
          </Badge>
          <Typography className="text-green-700" variant="text-button-sm">
            +{movement?.movement.toLocaleString()}
          </Typography>
        </div>
      );
    } else if (movement?.movement < 0) {
      return (
        <div className="flex items-center gap-2">
          <Badge color="red" size="sm">
            <div className="flex items-center gap-1">
              <ArrowDownIcon className="h-3 w-3" />
              <Typography variant="text-caption">Sold</Typography>
            </div>
          </Badge>
          <Typography className="text-red-700" variant="text-button-sm">
            {movement?.movement.toLocaleString()}
          </Typography>
        </div>
      );
    } else {
      return null;
    }
  };

  return (
    <div className="space-y-1 border-t pt-2 first:border-t-0 first:pt-0">
      <Typography variant="text-label-sm">
        {dayjs(movement?.date).format('DD MMM YYYY')}
      </Typography>
      {renderMovementType()}
      <Typography className="text-gray-700" variant="text-body-sm">
        {movement?.balance.toLocaleString()} net holdings
      </Typography>
    </div>
  );
};
