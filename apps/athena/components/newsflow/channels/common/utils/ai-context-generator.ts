import { JSONContent } from '@tiptap/core';
import { SocialPostPlatform, GetMediaQuery } from '@/apollo/generated';
import {
  convertToLinkedInFormat,
  extractPlainTextForTwitter,
} from '@/components/newsflow/channels/social-media/utils/content-formatters';

type DistributionType =
  | 'update'
  | 'linkedin'
  | 'twitter'
  | 'email'
  | 'announcement';

// Type alias for the media object from GraphQL query
type Media = NonNullable<GetMediaQuery['media']>;

interface AiContextOptions {
  currentContent?: JSONContent | string | null;
  currentDistribution: DistributionType;
  includeCurrentContent?: boolean;
}

/**
 * Safely extracts text content from JSONContent or string
 */
function extractTextContent(
  content: JSONContent | string | null | undefined | unknown,
  platform?: SocialPostPlatform
): string {
  if (!content) return '';

  if (typeof content === 'string') {
    return content;
  }

  // For social media platforms, use the appropriate formatter
  if (platform === SocialPostPlatform.Linkedin) {
    try {
      return convertToLinkedInFormat(content as JSONContent);
    } catch (e) {
      return '';
    }
  } else if (platform === SocialPostPlatform.Twitter) {
    try {
      return extractPlainTextForTwitter(content as JSONContent);
    } catch (e) {
      return '';
    }
  }

  // For other content types, stringify the JSON as fallback
  try {
    return JSON.stringify(content);
  } catch (e) {
    return '';
  }
}

/**
 * Generates AI context from media object for any distribution type
 */
interface Distribution {
  content?: string;
  header?: string;
  status: string;
  subject?: string;
  summary?: string;
  title?: string;
  type: string;
}

function collectDistributions(
  media: Media,
  currentDistribution?: string
): Distribution[] {
  const distributions: Distribution[] = [];

  // Add Update if available and not the current distribution
  if (
    currentDistribution !== 'update' &&
    media.mediaUpdate &&
    media.distributionUpdateEnabled
  ) {
    const updateContent = extractTextContent(media.mediaUpdate.contentDraft);

    distributions.push({
      content: updateContent || undefined,
      status: media.mediaUpdate.isDraft ? 'draft' : 'published',
      title: media.mediaUpdate.title || undefined,
      type: 'Blog Post Update',
    });
  }

  // Add LinkedIn if available and not the current distribution
  if (
    currentDistribution !== 'linkedin' &&
    media.linkedinSocialPost &&
    media.distributionLinkedinEnabled
  ) {
    const linkedinContent = extractTextContent(
      media.linkedinSocialPost.content,
      SocialPostPlatform.Linkedin
    );

    distributions.push({
      content: linkedinContent || undefined,
      status: media.linkedinSocialPost.status || 'draft',
      type: 'LinkedIn',
    });
  }

  // Add Twitter if available and not the current distribution
  if (
    currentDistribution !== 'twitter' &&
    media.twitterSocialPost &&
    media.distributionTwitterEnabled
  ) {
    const twitterContent = extractTextContent(
      media.twitterSocialPost.content,
      SocialPostPlatform.Twitter
    );

    distributions.push({
      content: twitterContent || undefined,
      status: media.twitterSocialPost.status || 'draft',
      type: 'Twitter',
    });
  }

  // Add Email if available and not the current distribution
  if (
    currentDistribution !== 'email' &&
    media.email &&
    media.distributionEmailEnabled
  ) {
    distributions.push({
      status: media.email.isDraft ? 'draft' : 'published',
      subject: media.email.subject || undefined,
      type: 'Email',
    });
  }

  // Add Announcement if available and not the current distribution
  if (
    currentDistribution !== 'announcement' &&
    media.mediaAnnouncement &&
    media.distributionAnnouncementEnabled
  ) {
    distributions.push({
      // Announcements are always published
      header: media.mediaAnnouncement.header || undefined,
      status: 'published',
      summary: media.mediaAnnouncement.summary || undefined,
      type: 'Announcement',
    });
  }

  return distributions;
}

function formatDistribution(dist: Distribution): string {
  let result = `${dist.type} (${dist.status}):\n`;

  if (dist.type === 'Blog Post Update') {
    if (dist.title) {
      result += `Title: ${dist.title}\n`;
    }
    if (dist.content) {
      result += `Content: ${dist.content}\n\n`;
    }
  } else if (dist.type === 'LinkedIn' || dist.type === 'Twitter') {
    if (dist.content) {
      result += `Content: ${dist.content}\n\n`;
    }
  } else if (dist.type === 'Email') {
    if (dist.subject) {
      result += `Subject: ${dist.subject}\n\n`;
    }
  } else if (dist.type === 'Announcement') {
    if (dist.header) {
      result += `Header: ${dist.header}\n`;
    }
    if (dist.summary) {
      result += `Summary: ${dist.summary}\n\n`;
    }
  }

  return result;
}

export function generateAiContext(
  media: GetMediaQuery['media'] | null | undefined,
  options: AiContextOptions
): string {
  if (!media) return '';

  const {
    currentContent,
    currentDistribution,
    includeCurrentContent = true,
  } = options;

  // Map distribution types to display names
  const distributionDisplayNames: Record<DistributionType, string> = {
    announcement: 'Announcement',
    email: 'Email',
    linkedin: 'LinkedIn',
    twitter: 'Twitter',
    update: 'Blog Post Update',
  };

  const currentDisplayName = distributionDisplayNames[currentDistribution];

  // Start with a descriptive introduction
  let context = `I need help with writing marketing content for my company. I am currently writing a ${currentDisplayName}, but this content has multiple distribution channels. Here is information about all the related content:\n\n`;

  // Add information about the current content if requested
  if (includeCurrentContent && currentContent) {
    context += `CURRENT ${currentDisplayName.toUpperCase()}:\n`;

    if (
      currentDistribution === 'linkedin' ||
      currentDistribution === 'twitter'
    ) {
      const platform =
        currentDistribution === 'linkedin'
          ? SocialPostPlatform.Linkedin
          : SocialPostPlatform.Twitter;
      const contentText = extractTextContent(currentContent, platform);
      if (contentText.trim()) {
        context += `Content: ${contentText}\n\n`;
      }
    } else if (currentDistribution === 'update') {
      const contentText = extractTextContent(currentContent);
      if (contentText.trim()) {
        context += `Content: ${contentText}\n\n`;
      }
    }
  }

  // Collect all distributions
  const distributions = collectDistributions(media, currentDistribution);

  // Add other distributions to context
  if (distributions.length > 0) {
    context += 'OTHER DISTRIBUTION CHANNELS:\n\n';
    distributions.forEach((dist) => {
      context += formatDistribution(dist);
    });
  }

  // Add instructions for AI
  context += 'INSTRUCTIONS FOR AI:\n';
  context +=
    'When I ask you to help with content, please consider all the distribution channels above. ';
  context +=
    'For example, if I ask you to "make this similar to the LinkedIn post", use the LinkedIn content as a reference. ';
  context +=
    'Or if I ask you to "write in a style consistent with our other content", analyze the style of all channels. ';
  context += `Focus on maintaining brand voice and messaging consistency across all channels. Remember this is for ${currentDisplayName}, so keep the content appropriate for that platform.`;

  return context;
}
