import { JSONContent } from '@tiptap/core';
import { SocialPostPlatform, GetMediaQuery } from '@/apollo/generated';
import { getDefaultSocialPostTemplate } from '@/utils/distribution-helpers';

// Extend the GraphQL type to include optional ticker information
type Media = NonNullable<GetMediaQuery['media']>;

interface TemplateContext {
  liveInvestorHubBaseUrl: string;
  media: Media;
  platform: SocialPostPlatform;
  tickerFromContext?: string;
}

/**
 * Construct URLs with UTM parameters matching the postURL() function
 */
function constructSocialPostUrls(context: TemplateContext) {
  const { liveInvestorHubBaseUrl, media, platform } = context;
  const utmSource = platform.toLowerCase();

  // Update URL
  const updateUrl = media.mediaUpdate?.slug
    ? `${liveInvestorHubBaseUrl}/activity-updates/${encodeURIComponent(
        media.mediaUpdate.slug
      )}?utm_medium=social&utm_source=${utmSource}&utm_campaign=mid-${
        media.mediaUpdate.mediaId
      }`
    : '';

  // Announcement URL (for live announcements)
  const announcementUrl = media.mediaAnnouncement?.id
    ? `${liveInvestorHubBaseUrl}/announcements/${encodeURIComponent(
        media.mediaAnnouncement.id
      )}?utm_medium=social&utm_source=${utmSource}&utm_campaign=mid-${
        media.mediaAnnouncement.mediaId
      }`
    : '';

  // Prepared announcement URL (with UTM parameters)
  const preparedAnnouncementUrl = media.preparedAnnouncement?.hashId
    ? `${liveInvestorHubBaseUrl}/link/${encodeURIComponent(
        media.preparedAnnouncement.hashId
      )}?utm_medium=social&utm_source=${utmSource}&utm_campaign=mid-${media.id}`
    : '';

  return {
    announcementUrl,
    preparedAnnouncementUrl,
    updateUrl,
  };
}

/**
 * Get template variables for the given context
 */
function getTemplateVariables(context: TemplateContext) {
  const { media, tickerFromContext } = context;
  const urls = constructSocialPostUrls(context);

  const announcementUrl = media.preparedAnnouncement?.hashId
    ? urls.preparedAnnouncementUrl
    : urls.announcementUrl;

  return {
    // Announcement variables
    announcement_title:
      media.mediaAnnouncement?.header ||
      media.preparedAnnouncement?.title ||
      media.title ||
      'Our Latest Announcement',

    announcement_url: announcementUrl,

    // Common variables
    ticker: tickerFromContext || 'TICKER',

    // Update variables
    update_title:
      media.mediaUpdate?.title || media.title || 'Our Latest Update',

    update_url: urls.updateUrl,
  };
}

/**
 * Replace template variables in a string
 */
function replaceTemplateVariables(
  template: string,
  variables: Record<string, string>
) {
  return template.replace(/\{\{\s*(\w+)\s*\}\}/g, (match, key) => {
    return variables[key] || match; // Return original if variable not found
  });
}

/**
 * Convert plain text template to TipTap JSON format
 */
function convertTextToTipTapJson(text: string): JSONContent {
  // Split text by newlines and create paragraphs
  const lines = text.split('\n');

  const content = lines.map((line) => {
    if (line.trim() === '') {
      // Empty line - create empty paragraph
      return {
        content: [],
        type: 'paragraph',
      };
    } else {
      // Line with text
      return {
        content: [
          {
            text: line,
            type: 'text',
          },
        ],
        type: 'paragraph',
      };
    }
  });

  return {
    content,
    type: 'doc',
  };
}

/**
 * Generate pre-filled social post content based on media type and platform
 */
export function generateSocialPostTemplate(
  context: TemplateContext
): JSONContent | null {
  const { media } = context;

  // Check if we should generate a template (distribution enabled + content exists)
  const hasUpdate = media.distributionUpdateEnabled && media.mediaUpdate?.slug;
  const hasAnnouncement =
    media.distributionAnnouncementEnabled &&
    (media.preparedAnnouncement?.hashId || media.mediaAnnouncement?.id);

  if (!hasUpdate && !hasAnnouncement) {
    return null; // No content to template
  }

  // Determine content type (prioritize announcements)
  const contentType = hasAnnouncement ? 'announcement' : 'update';

  // Get template and variables
  const template = getDefaultSocialPostTemplate(contentType);
  const variables = getTemplateVariables(context);

  // Replace variables and convert to TipTap format
  const populatedTemplate = replaceTemplateVariables(template, variables);
  return convertTextToTipTapJson(populatedTemplate);
}

/**
 * Check if content is empty/default (should be replaced with template)
 */
export function isContentEmpty(content: JSONContent): boolean {
  if (!content || !content.content) return true;

  // Check if it's the default empty structure
  if (content.content.length === 1) {
    const firstNode = content.content[0];
    if (
      firstNode.type === 'paragraph' &&
      (!firstNode.content || firstNode.content.length === 0)
    ) {
      return true;
    }
  }

  return false;
}
