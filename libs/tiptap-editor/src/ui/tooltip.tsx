import React from 'react';
import { Tooltip as ReactTooltip } from 'react-tooltip';
import 'react-tooltip/dist/react-tooltip.css';

// Simple wrapper to match our interface expectations
interface TooltipProviderProps {
  children: React.ReactNode;
}

export const TooltipProvider: React.FC<TooltipProviderProps> = ({
  children,
}) => {
  return children as React.ReactElement;
};

interface TooltipProps {
  children: React.ReactNode;
}

export const Tooltip: React.FC<TooltipProps> = ({ children }) => {
  return children as React.ReactElement;
};

interface TooltipTriggerProps {
  children: React.ReactNode;
}

export const TooltipTrigger: React.FC<TooltipTriggerProps> = ({ children }) => {
  return children as React.ReactElement;
};

interface TooltipContentProps {
  children: React.ReactNode;
}

export const TooltipContent: React.FC<TooltipContentProps> = ({ children }) => {
  return children as React.ReactElement;
};

// Export the actual ReactTooltip component for direct use
export { ReactTooltip };
