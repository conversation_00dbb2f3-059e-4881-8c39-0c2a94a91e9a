'use client';
/* eslint-disable @nx/enforce-module-boundaries */
import { useEffect, useState, useMemo, useRef, useCallback } from 'react';
import { useDebouncedCallback } from 'use-debounce';
import { defaultEditorContent } from './dummy-content';
import { createExtensions } from './extensions';

import GenerativeMenuSwitch from './generative/generative-menu-switch';
import { createUploadFn } from './image-upload';
import { cn } from './lib/utils';
import {
  EditorCommand,
  EditorCommandEmpty,
  EditorCommandItem,
  EditorCommandList,
  EditorContent,
  type EditorInstance,
  EditorRoot,
  ImageResizer,
  type JSONContent,
  handleCommandNavigation,
  handleImageDrop,
  handleImagePaste,
} from './novel/src';
import { CustomDragHandle } from './selectors/custom-drag-handle';
import { LinkSelector } from './selectors/link-selector';
import { NodeSelector } from './selectors/node-selector';
import { PermanentToolbar } from './selectors/permanent-toolbar';
import { TextButtons } from './selectors/text-buttons';
import { ToolbarSlashCommands } from './selectors/toolbar-slash-commands';
import { createSuggestionItems } from './slash-command';
import { Separator } from './ui/separator';

import { BlockType, DEFAULT_BLOCKS, isBlockAllowed } from './utils/block-types';

export interface ShortcutLink {
  icon?: React.ReactNode;
  name: string;
  url: string;
}

interface AdvancedEditorProps {
  aiContext?: string;
  blocks?: BlockType[];
  companyId: string;
  content: JSONContent;
  debounceTime?: number;
  heightClass?: string;
  onChange?: (content: {
    html: string;
    json: JSONContent;
    markdown: string;
  }) => void;
  onCharsCountChange?: (count: number) => void;
  onEditorReady?: (editor: EditorInstance) => void;
  shortcutLinks?: ShortcutLink[];
  showPermanentToolbar?: boolean;
}

const AdvancedEditor = ({
  aiContext,
  blocks = DEFAULT_BLOCKS,
  companyId,
  content,
  debounceTime = 500,
  heightClass = 'min-h-[500px]',
  onChange,
  onCharsCountChange,
  onEditorReady,
  shortcutLinks,
  showPermanentToolbar = true,
}: AdvancedEditorProps) => {
  const [initialContent, setInitialContent] = useState<null | JSONContent>(
    content
  );
  const [editorInstance, setEditorInstance] = useState<EditorInstance | null>(
    null
  );

  // We don't need to track charsCount in state since we're passing it directly to onCharsCountChange
  const [openNode, setOpenNode] = useState(false);
  const [openLink, setOpenLink] = useState(false);
  const [aiModalOpen, setAiModalOpen] = useState(false);

  const uploadFn = useMemo(() => createUploadFn(companyId), [companyId]);
  const extensions = useMemo(
    () => createExtensions({ allowedBlocks: blocks, companyId, shortcutLinks }),
    [companyId, blocks, shortcutLinks]
  );
  const suggestionItems = useMemo(
    () => createSuggestionItems(companyId, blocks, shortcutLinks),
    [companyId, blocks, shortcutLinks]
  );

  const debouncedUpdates = useDebouncedCallback(
    async (editor: EditorInstance) => {
      const json = editor.getJSON();
      const wordCount = editor.storage.characterCount.words();
      // Pass the word count directly to the callback
      onCharsCountChange?.(wordCount);
      onChange?.({
        html: editor.getHTML(),
        json: json,
        markdown: editor.storage.markdown.getMarkdown(),
      });
    },
    debounceTime
  );

  // Create a ref to the editor content element
  const editorRef = useRef<HTMLDivElement>(null);

  // Function to focus the editor when clicking anywhere in the editor area
  const focusEditor = useCallback(() => {
    const editor = editorRef.current?.querySelector('.ProseMirror');
    if (editor) {
      (editor as HTMLElement).focus();
    }
  }, []);

  useEffect(() => {
    if (content) setInitialContent(content);
    else setInitialContent(defaultEditorContent);
  }, [content]);

  // Apply the minimum height to the ProseMirror editor when it's mounted
  useEffect(() => {
    if (!editorRef.current) return;

    // Function to set the height of the ProseMirror editor
    const setEditorHeight = () => {
      const editorElement = editorRef.current?.querySelector('.ProseMirror');
      if (editorElement) {
        // Extract the pixel value from the heightClass (e.g., 'min-h-[500px]' -> '500px')
        const heightValue = heightClass.match(/\[(.*?)\]/)?.[1] || '500px';
        (editorElement as HTMLElement).style.minHeight = heightValue;
        (editorElement as HTMLElement).style.height = '100%';
      }
    };

    // Set the height initially
    setEditorHeight();

    // Create a MutationObserver to detect when the editor is added to the DOM
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
          const editorElement =
            editorRef.current?.querySelector('.ProseMirror');
          if (editorElement) {
            setEditorHeight();
            observer.disconnect(); // Stop observing once we've found and set the editor
          }
        }
      });
    });

    // Start observing the editor container for changes
    observer.observe(editorRef.current, { childList: true, subtree: true });

    return () => {
      observer.disconnect();
    };
  }, [heightClass]);

  if (!initialContent) return null;

  return (
    <div
      className="relative w-full max-w-screen-lg font-system"
      onClick={focusEditor}
    >
      <EditorRoot>
        {showPermanentToolbar && (
          <PermanentToolbar top={65}>
            <ToolbarSlashCommands
              blocks={blocks}
              companyId={companyId}
              editor={editorInstance}
              shortcutLinks={shortcutLinks}
            />
          </PermanentToolbar>
        )}
        <EditorContent
          ref={editorRef}
          className={`relative ${heightClass} bg-background w-full max-w-screen-lg border-x border-b border-gray-200`}
          editorProps={{
            attributes: {
              class: cn(
                'prose prose-lg dark:prose-invert prose-headings:font-title font-default focus:outline-none max-w-full tiptap px-6 py-4'
              ),
            },
            handleDOMEvents: {
              keydown: (_, event) => {
                // Handle cmd-k/ctrl-k for AI modal
                if ((event.metaKey || event.ctrlKey) && event.key === 'k') {
                  event.preventDefault();
                  setAiModalOpen(true);
                  return true;
                }
                return handleCommandNavigation(event);
              },
            },
            handleDrop: (view, event, _slice, moved) =>
              handleImageDrop(view, event, moved, uploadFn),
            handlePaste: (view, event) =>
              handleImagePaste(view, event, uploadFn),
          }}
          extensions={extensions}
          immediatelyRender={false}
          initialContent={initialContent}
          slotAfter={<ImageResizer />}
          onCreate={({ editor }) => {
            // Extract the pixel value from the heightClass (e.g., 'min-h-[500px]' -> '500px')
            const heightValue = heightClass.match(/\[(.*?)\]/)?.[1] || '500px';

            // Set the height directly on the editor DOM element
            const editorElement = editor.view.dom;
            editorElement.style.minHeight = heightValue;
            editorElement.style.height = '100%';

            // Store editor instance in local state for toolbar
            setEditorInstance(editor);

            onEditorReady?.(editor);
          }}
          onUpdate={({ editor }) => {
            debouncedUpdates(editor);
          }}
        >
          <EditorCommand className="border-muted bg-background z-50 h-auto max-h-[330px] overflow-y-auto rounded-md border px-1 py-2 shadow-md transition-all">
            <EditorCommandEmpty className="text-muted-foreground px-2">
              No results
            </EditorCommandEmpty>
            <EditorCommandList>
              {suggestionItems.map((item) => (
                <EditorCommandItem
                  key={item.title}
                  className="hover:bg-accent aria-selected:bg-accent flex w-full items-center space-x-2 rounded-md px-2 py-1 text-left text-sm"
                  value={item.title}
                  onCommand={(val) => item.command?.(val)}
                >
                  <div className="border-muted bg-background flex h-10 w-10 items-center justify-center rounded-md border">
                    {item.icon}
                  </div>
                  <div>
                    <p className="font-medium">{item.title}</p>
                    <p className="text-muted-foreground text-xs">
                      {item.description}
                    </p>
                  </div>
                </EditorCommandItem>
              ))}
            </EditorCommandList>
          </EditorCommand>
          <CustomDragHandle />
          <GenerativeMenuSwitch
            aiContext={aiContext}
            aiModalOpen={aiModalOpen}
            onOpenChange={setAiModalOpen}
          >
            <Separator orientation="vertical" />
            <NodeSelector
              allowedBlocks={blocks}
              open={openNode}
              onOpenChange={setOpenNode}
            />
            <Separator orientation="vertical" />
            {isBlockAllowed('link', blocks) && (
              <LinkSelector open={openLink} onOpenChange={setOpenLink} />
            )}
            <Separator orientation="vertical" />
            <TextButtons blocks={blocks} />
          </GenerativeMenuSwitch>
        </EditorContent>
      </EditorRoot>
    </div>
  );
};

export default AdvancedEditor;
