import { cx } from 'class-variance-authority';
import {
  AIHighlight,
  CharacterCount,
  CustomKeymap,
  GlobalDragHandle,
  HorizontalRule,
  MarkdownExtension,
  Placeholder,
  StarterKit,
  TaskItem,
  TaskList,
  TextStyle,
  TiptapImage,
  TiptapLink,
  TiptapUnderline,
  Twitter,
  UpdatedImage,
  UploadImagesPlugin,
  Youtube,
  AutoNewline,
  ImageSelectionHighlightPlugin,
} from './novel/src';
import { createSlashCommand } from './slash-command';
import {
  BlockType,
  DEFAULT_BLOCKS,
  isBlockAllowed,
  isMarkAllowed,
} from './utils/block-types';
import type { ShortcutLink } from './advanced-editor';
import type { AnyExtension } from '@tiptap/core';

//TODO I am using cx here to get tailwind autocomplete working, idk if someone else can write a regex to just capture the class key in objects
const aiHighlight = AIHighlight;

const autoNewline = AutoNewline.configure({
  autoAppendParagraph: true,
  newlineInCodeKey: 'Enter',
});
//You can overwrite the placeholder with your own configuration
const placeholder = Placeholder;
const tiptapLink = TiptapLink.configure({
  HTMLAttributes: {
    class: cx(
      'text-muted-foreground underline underline-offset-[3px] hover:text-primary transition-colors cursor-pointer'
    ),
  },
});

export interface CreateExtensionsOptions {
  allowedBlocks?: BlockType[];
  companyId?: string;
  shortcutLinks?: ShortcutLink[];
}

export const createExtensions = ({
  companyId,
  allowedBlocks = DEFAULT_BLOCKS,
  shortcutLinks,
}: CreateExtensionsOptions) => {
  const tiptapImage = TiptapImage.extend({
    addProseMirrorPlugins() {
      const uploadPlugin = UploadImagesPlugin({
        imageClass: cx('opacity-40 rounded-lg border border-stone-200'),
      });

      const selectionHighlightPlugin = ImageSelectionHighlightPlugin();

      return [uploadPlugin, selectionHighlightPlugin];
    },
    name: 'image-upload',
  }).configure({
    HTMLAttributes: {
      class: cx('rounded-lg border border-muted'),
    },
    allowBase64: true,
  });

  const dragHandle = GlobalDragHandle.configure({
    dragHandleSelector: '.custom-drag-handle',
  });

  const updatedImage = UpdatedImage.configure({
    HTMLAttributes: {
      class: cx('rounded-lg border border-muted'),
    },
  });

  const taskList = TaskList.configure({
    HTMLAttributes: {
      class: cx('not-prose pl-2 '),
    },
  });
  const taskItem = TaskItem.configure({
    HTMLAttributes: {
      class: cx('flex gap-2 items-start my-4'),
    },
    nested: true,
  });

  const horizontalRule = HorizontalRule.configure({
    HTMLAttributes: {
      class: cx('mt-4 mb-6 border-t border-muted-foreground'),
    },
  });

  const starterKit = StarterKit.configure({
    blockquote: {
      HTMLAttributes: {
        class: cx('border-l-4 border-primary'),
      },
    },
    bold: isMarkAllowed('bold', allowedBlocks) ? undefined : false,
    bulletList: {
      HTMLAttributes: {
        class: cx('list-disc list-outside leading-3 -mt-2'),
      },
    },
    code: false,
    codeBlock: false,
    dropcursor: {
      color: '#DBEAFE',
      width: 4,
    },
    gapcursor: false,
    horizontalRule: false,
    italic: isMarkAllowed('italic', allowedBlocks) ? undefined : false,
    listItem: {
      HTMLAttributes: {
        class: cx('leading-normal -mb-2'),
      },
    },
    orderedList: {
      HTMLAttributes: {
        class: cx('list-decimal list-outside leading-3 -mt-2'),
      },
    },
  });

  const youtube = Youtube.configure({
    HTMLAttributes: {
      class: cx('rounded-lg border border-muted'),
    },
    inline: false,
  });

  const twitter = Twitter.configure({
    HTMLAttributes: {
      class: cx('not-prose'),
    },
    inline: false,
  });

  const characterCount = CharacterCount.configure();

  const markdownExtension = MarkdownExtension.configure({
    breaks: false,
    bulletListMarker: '-',
    html: true,
    linkify: false,
    tightListClass: 'tight',
    tightLists: true,
    transformCopiedText: false,
    transformPastedText: false,
  });

  const slashCommand = companyId
    ? createSlashCommand(companyId, allowedBlocks, shortcutLinks)
    : null;

  // Configure StarterKit based on allowed blocks
  const configuredStarterKit = StarterKit.configure({
    ...starterKit.options,
    blockquote: isBlockAllowed('blockquote', allowedBlocks)
      ? starterKit.options.blockquote
      : false,
    bold: isMarkAllowed('bold', allowedBlocks) ? undefined : false,
    bulletList: isBlockAllowed('bulletList', allowedBlocks)
      ? starterKit.options.bulletList
      : false,
    codeBlock: isBlockAllowed('codeBlock', allowedBlocks)
      ? starterKit.options.codeBlock
      : false,
    heading:
      isBlockAllowed('heading1', allowedBlocks) ||
      isBlockAllowed('heading2', allowedBlocks) ||
      isBlockAllowed('heading3', allowedBlocks)
        ? starterKit.options.heading
        : false,
    horizontalRule: false,

    italic: isMarkAllowed('italic', allowedBlocks) ? undefined : false,
    // We use our custom horizontalRule
    orderedList: isBlockAllowed('orderedList', allowedBlocks)
      ? starterKit.options.orderedList
      : false,
  });

  // Always include required extensions
  const requiredExtensions = [
    configuredStarterKit,
    placeholder,
    tiptapLink,
    characterCount,
    TiptapUnderline,
    markdownExtension,
    TextStyle,
    CustomKeymap,
    dragHandle,
    autoNewline,
    aiHighlight,
  ];

  // Add optional extensions based on allowed blocks
  const optionalExtensions = [
    isBlockAllowed('image', allowedBlocks) ? tiptapImage : null,
    isBlockAllowed('image', allowedBlocks) ? updatedImage : null,
    isBlockAllowed('taskList', allowedBlocks) ? taskList : null,
    isBlockAllowed('taskList', allowedBlocks) ? taskItem : null,
    isBlockAllowed('horizontalRule', allowedBlocks) ? horizontalRule : null,
    isBlockAllowed('youtube', allowedBlocks) ? youtube : null,
    isBlockAllowed('twitter', allowedBlocks) ? twitter : null,
  ].filter(Boolean) as AnyExtension[];

  return [
    ...requiredExtensions,
    ...optionalExtensions,
    ...(slashCommand ? [slashCommand] : []),
  ];
};
