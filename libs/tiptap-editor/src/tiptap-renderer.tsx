import { useEffect, useState } from 'react';
import { Editor } from '@tiptap/core';
import { type JSONContent } from '@tiptap/react';
import { createExtensions } from './extensions';

interface TiptapRendererProps {
  className?: string;
  companyId?: string;
  content: JSONContent;
}

export const TiptapRenderer = ({
  className = '',
  companyId,
  content,
}: TiptapRendererProps) => {
  const extensions = createExtensions({ companyId });
  const [html, setHtml] = useState<string>('');

  useEffect(() => {
    const tempEditor = new Editor({
      editable: false,
      extensions,
    });

    tempEditor.commands.setContent(content);
    setHtml(tempEditor.getHTML());

    // Cleanup
    return () => {
      tempEditor.destroy();
    };
  }, [content, extensions]);

  return (
    <div
      dangerouslySetInnerHTML={{ __html: html }}
      className={`prose prose-lg dark:prose-invert ${className}`}
    />
  );
};
